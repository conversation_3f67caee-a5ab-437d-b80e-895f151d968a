# NoteNest Deployment Guide 🚀

This guide covers how to deploy your NoteNest app to production.

## Pre-Deployment Checklist ✅

### 1. Configuration
- [ ] Firebase configuration is set up correctly
- [ ] Google Drive API is enabled and configured
- [ ] Google Drive folder structure is created
- [ ] App configuration in `src/config/app.ts` is updated
- [ ] All placeholder values are replaced with real credentials

### 2. Testing
- [ ] App runs successfully in development (`npm start`)
- [ ] Google authentication works
- [ ] Google Drive integration functions properly
- [ ] All screens navigate correctly
- [ ] File viewing works as expected
- [ ] Search functionality operates correctly

### 3. Security
- [ ] API keys are secured (not committed to public repos)
- [ ] Firebase security rules are configured
- [ ] Google Drive permissions are properly set
- [ ] OAuth redirect URIs are configured for production

## Deployment Options 📱

### Option 1: Expo Application Services (EAS) - Recommended

EAS is the modern way to build and deploy Expo apps.

#### Setup EAS:
```bash
# Install EAS CLI
npm install -g eas-cli

# Login to your Expo account
eas login

# Initialize EAS in your project
cd NoteNest
eas build:configure
```

#### Configure EAS Build:
Create `eas.json` in your project root:

```json
{
  "cli": {
    "version": ">= 5.2.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "autoIncrement": true
    }
  },
  "submit": {
    "production": {}
  }
}
```

#### Build for Android:
```bash
# Build APK for testing
eas build --platform android --profile preview

# Build AAB for Google Play Store
eas build --platform android --profile production
```

#### Build for iOS:
```bash
# Build for TestFlight/App Store
eas build --platform ios --profile production
```

### Option 2: Expo Classic Build (Legacy)

If you prefer the classic build service:

```bash
# Build for Android
expo build:android

# Build for iOS
expo build:ios
```

## Environment Configuration 🔧

### Production Environment Variables:

Create a `.env.production` file:

```bash
# Firebase Configuration
FIREBASE_API_KEY=your_production_api_key
FIREBASE_AUTH_DOMAIN=your_production_auth_domain
FIREBASE_PROJECT_ID=your_production_project_id
FIREBASE_STORAGE_BUCKET=your_production_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_production_sender_id
FIREBASE_APP_ID=your_production_app_id

# Google Drive Configuration
GOOGLE_DRIVE_FOLDER_ID=your_production_folder_id
GOOGLE_OAUTH_CLIENT_ID=your_production_client_id
```

### Update App Configuration:

Modify `src/config/app.ts` to use environment variables:

```typescript
import Constants from 'expo-constants';

const isProduction = Constants.appOwnership === 'standalone';

export const APP_CONFIG: AppConfig = {
  googleDriveFolderId: isProduction 
    ? process.env.GOOGLE_DRIVE_FOLDER_ID 
    : 'YOUR_DEV_FOLDER_ID',
  
  firebaseConfig: {
    apiKey: isProduction 
      ? process.env.FIREBASE_API_KEY 
      : 'YOUR_DEV_API_KEY',
    // ... other config
  },
};
```

## App Store Deployment 📲

### Google Play Store (Android):

1. **Prepare Release:**
   - Build production AAB with EAS
   - Test thoroughly on different devices
   - Prepare app store listing materials

2. **Upload to Play Console:**
   - Create app in Google Play Console
   - Upload AAB file
   - Fill out store listing information
   - Set up pricing and distribution

3. **Review Process:**
   - Submit for review
   - Address any feedback
   - Release to production

### Apple App Store (iOS):

1. **Prepare Release:**
   - Build production IPA with EAS
   - Test on physical iOS devices
   - Prepare app store assets

2. **Upload to App Store Connect:**
   - Create app in App Store Connect
   - Upload IPA using Transporter or EAS Submit
   - Fill out app information

3. **Review Process:**
   - Submit for App Store review
   - Address any rejections
   - Release to App Store

## Post-Deployment 📊

### Monitoring:

1. **Firebase Analytics:**
   - Monitor user engagement
   - Track authentication success rates
   - Analyze feature usage

2. **Crashlytics:**
   - Monitor app crashes
   - Track error rates
   - Fix critical issues quickly

3. **Performance Monitoring:**
   - Monitor app startup time
   - Track API response times
   - Optimize slow operations

### Updates:

1. **Over-the-Air Updates (OTA):**
   ```bash
   # Publish updates without app store review
   eas update --branch production
   ```

2. **App Store Updates:**
   - For native code changes
   - Major feature releases
   - Follow same deployment process

## Troubleshooting 🔧

### Common Deployment Issues:

1. **Build Failures:**
   - Check all dependencies are compatible
   - Verify configuration files are correct
   - Review build logs for specific errors

2. **Authentication Issues:**
   - Verify OAuth redirect URIs for production
   - Check Firebase configuration
   - Ensure API keys are correct

3. **Google Drive Access:**
   - Verify API is enabled in production project
   - Check folder permissions
   - Confirm service account setup (if using)

### Debug Production Issues:

1. **Enable Remote Debugging:**
   ```typescript
   // In production builds
   if (__DEV__) {
     console.log('Debug info:', data);
   }
   ```

2. **Use Flipper (for development builds):**
   - Network inspection
   - Redux state monitoring
   - Performance profiling

## Security Best Practices 🔒

### API Key Management:
- Never commit real API keys to version control
- Use environment variables or secure storage
- Rotate keys regularly
- Monitor API usage for anomalies

### Firebase Security:
- Set up proper security rules
- Enable App Check for additional security
- Monitor authentication logs
- Use Firebase Security Rules simulator

### Google Drive Security:
- Use least-privilege access
- Monitor folder access logs
- Set up proper sharing permissions
- Consider using service accounts for server-side access

## Performance Optimization ⚡

### Bundle Size:
- Use Expo bundle analyzer
- Remove unused dependencies
- Optimize images and assets
- Enable Hermes for Android

### Runtime Performance:
- Implement proper caching strategies
- Use FlatList for large lists
- Optimize image loading
- Minimize re-renders

---

## Final Checklist Before Release 🎯

- [ ] All features tested on physical devices
- [ ] Performance is acceptable on low-end devices
- [ ] App works offline (cached data)
- [ ] Error handling is comprehensive
- [ ] User experience is smooth and intuitive
- [ ] Privacy policy and terms of service are ready
- [ ] App store assets are prepared
- [ ] Analytics and crash reporting are configured
- [ ] Backup and recovery procedures are in place

**Your NoteNest app is ready for the world! 🌟**
