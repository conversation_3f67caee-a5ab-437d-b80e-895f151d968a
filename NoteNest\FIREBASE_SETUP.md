# Firebase Setup Guide for NoteNest 🔥

This guide will walk you through setting up Firebase Authentication with Google Sign-In for the NoteNest app.

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click **"Create a project"** or **"Add project"**
3. Enter project name: `NoteN<PERSON>` (or your preferred name)
4. Choose whether to enable Google Analytics (optional)
5. Click **"Create project"**

## Step 2: Enable Authentication

1. In your Firebase project dashboard, click **"Authentication"** in the left sidebar
2. Click **"Get started"** if it's your first time
3. Go to the **"Sign-in method"** tab
4. Click on **"Google"** from the list of providers
5. Toggle the **"Enable"** switch
6. Enter your project support email
7. Click **"Save"**

## Step 3: Get Firebase Configuration

1. Click the **gear icon** ⚙️ next to "Project Overview"
2. Select **"Project settings"**
3. Scroll down to **"Your apps"** section
4. Click **"Add app"** and select the platform icon (iOS/Android/Web)
5. Register your app:
   - **iOS Bundle ID**: `com.notenest.app`
   - **Android Package Name**: `com.notenest.app`
   - **App Name**: `NoteNest`
6. Download the configuration file or copy the config object

### Firebase Config Object Example:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC...",
  authDomain: "notenest-12345.firebaseapp.com",
  projectId: "notenest-12345",
  storageBucket: "notenest-12345.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

## Step 4: Configure Google OAuth

### For Web/Expo Development:

1. In Firebase Console, go to **Authentication > Sign-in method**
2. Click on **Google** provider
3. In the **"Authorized domains"** section, add:
   - `localhost` (for development)
   - Your Expo development URL
   - Your production domain (if applicable)

### For Production Apps:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your Firebase project
3. Go to **APIs & Services > Credentials**
4. Find your OAuth 2.0 Client ID
5. Add your app's bundle identifier to **Authorized redirect URIs**

## Step 5: Update App Configuration

1. Open `src/config/app.ts` in your NoteNest project
2. Replace the placeholder values with your actual Firebase config:

```typescript
export const APP_CONFIG: AppConfig = {
  googleDriveFolderId: 'YOUR_GOOGLE_DRIVE_FOLDER_ID', // We'll set this up next
  firebaseConfig: {
    apiKey: 'YOUR_ACTUAL_API_KEY',
    authDomain: 'your-project-id.firebaseapp.com',
    projectId: 'your-project-id',
    storageBucket: 'your-project-id.appspot.com',
    messagingSenderId: 'your-sender-id',
    appId: 'your-app-id',
  },
};
```

## Step 6: Set Up Google Drive API

### Enable Google Drive API:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your Firebase project
3. Go to **APIs & Services > Library**
4. Search for **"Google Drive API"**
5. Click on it and press **"Enable"**

### Create OAuth Credentials:

1. Go to **APIs & Services > Credentials**
2. Click **"Create Credentials"** > **"OAuth client ID"**
3. Choose **"Web application"** for Expo development
4. Add authorized redirect URIs:
   - For Expo: `https://auth.expo.io/@your-username/notenest`
   - For production: your app's custom scheme

### Get Client ID:

1. Copy the **Client ID** from your OAuth credentials
2. You'll need this for the Google Sign-In configuration

## Step 7: Configure Google Drive Folder

### Create Shared Folder:

1. Go to [Google Drive](https://drive.google.com/)
2. Create a new folder called **"University Notes"** (or your preferred name)
3. Inside this folder, create subfolders for each semester:
   - Semester 1
   - Semester 2
   - Semester 3
   - etc.

### Get Folder ID:

1. Open your main folder in Google Drive
2. Look at the URL: `https://drive.google.com/drive/folders/FOLDER_ID_HERE`
3. Copy the folder ID from the URL
4. Update `googleDriveFolderId` in your app config

### Set Folder Permissions:

1. Right-click on your main folder
2. Select **"Share"**
3. Change permissions to **"Anyone with the link can view"**
4. Or add specific email addresses of users who should have access

## Step 8: Test Authentication

1. Run your app: `npm start`
2. Try signing in with Google
3. Check Firebase Console > Authentication > Users to see if the user was created

## Step 9: Security Rules (Optional)

For additional security, you can set up Firebase Security Rules:

```javascript
// Firestore Security Rules (if using Firestore)
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Troubleshooting 🔧

### Common Issues:

1. **"Auth domain not authorized"**:
   - Add your domain to authorized domains in Firebase Console

2. **"Invalid client ID"**:
   - Check that your OAuth client ID is correct
   - Ensure the bundle ID matches your app configuration

3. **"Drive API not enabled"**:
   - Enable Google Drive API in Google Cloud Console

4. **"Insufficient permissions"**:
   - Check that your Google Drive folder has proper sharing permissions

5. **"Network error"**:
   - Ensure your app has internet permissions
   - Check Firebase configuration

### Debug Tips:

1. Enable debug mode in your app
2. Check browser console for error messages
3. Verify all configuration values are correct
4. Test with a simple Firebase auth example first

## Production Considerations 🚀

### Security:

1. **Never commit** your Firebase config with real keys to public repositories
2. Use environment variables for sensitive data
3. Set up proper Firebase Security Rules
4. Regularly rotate API keys

### Performance:

1. Enable Firebase Analytics for usage insights
2. Set up Firebase Performance Monitoring
3. Use Firebase Crashlytics for error tracking

### Deployment:

1. Update authorized domains for production
2. Configure proper OAuth redirect URIs
3. Test authentication flow on production builds

---

## Next Steps

After completing this setup:

1. ✅ Firebase Authentication is configured
2. ✅ Google Drive API is enabled
3. ✅ App configuration is updated
4. 🔄 Test the complete authentication flow
5. 🔄 Upload some test files to your Google Drive folder
6. 🔄 Test file access and viewing in the app

**You're ready to use NoteNest! 🎉**
