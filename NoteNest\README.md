# NoteNest 📚

A modern React Native + Expo mobile app that allows users to sign in with Google and access university notes stored in Google Drive, organized semester-wise.

## Features ✨

- **Google Authentication**: Secure sign-in with Google accounts
- **Google Drive Integration**: Access notes from a shared Google Drive folder
- **Semester Organization**: Notes organized by semester folders
- **Smart Search**: Search across all semesters by file name
- **File Viewer**: In-app preview for PDFs and documents
- **Modern UI**: Clean, minimalistic design with pastel colors
- **Offline Support**: Cached data for better performance

## Tech Stack 🛠️

- **React Native** with **Expo**
- **Firebase Authentication** (Google Sign-In)
- **Google Drive REST API**
- **React Navigation** for screen management
- **AsyncStorage** for local caching
- **TypeScript** for type safety

## Prerequisites 📋

Before setting up the app, you'll need:

1. **Node.js** (v16 or higher)
2. **Expo CLI**: `npm install -g @expo/cli`
3. **Firebase Project** with Google Authentication enabled
4. **Google Drive API** credentials
5. **Expo account** (for building and testing)

## Setup Instructions 🚀

### 1. Clone and Install Dependencies

```bash
cd NoteNest
npm install
```

### 2. Firebase Configuration

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or use an existing one
3. Enable **Authentication** and add **Google** as a sign-in provider
4. Get your Firebase configuration object
5. Update `src/config/app.ts` with your Firebase config:

```typescript
export const APP_CONFIG: AppConfig = {
  googleDriveFolderId: 'YOUR_GOOGLE_DRIVE_FOLDER_ID',
  firebaseConfig: {
    apiKey: 'YOUR_FIREBASE_API_KEY',
    authDomain: 'YOUR_PROJECT_ID.firebaseapp.com',
    projectId: 'YOUR_PROJECT_ID',
    storageBucket: 'YOUR_PROJECT_ID.appspot.com',
    messagingSenderId: 'YOUR_MESSAGING_SENDER_ID',
    appId: 'YOUR_APP_ID',
  },
};
```

### 3. Google Drive Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the **Google Drive API**
3. Create credentials (OAuth 2.0 Client ID)
4. Add your app's bundle identifier to authorized domains
5. Create a shared Google Drive folder with semester subfolders
6. Get the folder ID from the URL and update `googleDriveFolderId` in config

### 4. Google Drive Folder Structure

Organize your Google Drive folder like this:

```
📁 University Notes (Main Folder)
├── 📁 Semester 1
│   ├── 📄 Mathematics.pdf
│   ├── 📄 Physics.pdf
│   └── 📄 Chemistry.pdf
├── 📁 Semester 2
│   ├── 📄 Advanced Math.pdf
│   └── 📄 Computer Science.pdf
└── 📁 Semester 3
    └── 📄 Data Structures.pdf
```

### 5. Run the App

```bash
# Start the development server
npm start

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android

# Run on web
npm run web
```

## Supported File Types 📄

- **PDF** (.pdf)
- **Word Documents** (.doc, .docx)
- **PowerPoint** (.ppt, .pptx)
- **Text Files** (.txt)

## App Structure 📁

```
src/
├── auth/
│   └── LoginScreen.tsx
├── screens/
│   ├── HomeScreen.tsx
│   ├── SemesterScreen.tsx
│   ├── FileViewer.tsx
│   └── SearchScreen.tsx
├── components/
│   ├── FileCard.tsx
│   ├── SemesterCard.tsx
│   ├── LoadingSpinner.tsx
│   └── EmptyState.tsx
├── services/
│   ├── auth.ts
│   └── driveApi.ts
├── types/
│   └── index.ts
├── config/
│   └── app.ts
└── utils/
```

## Building for Production 🏗️

### Using EAS Build (Recommended)

1. Install EAS CLI: `npm install -g eas-cli`
2. Login to Expo: `eas login`
3. Configure build: `eas build:configure`
4. Build for Android: `eas build --platform android`
5. Build for iOS: `eas build --platform ios`

### Using Expo Build (Legacy)

```bash
expo build:android
expo build:ios
```

## Environment Variables 🔐

For production, consider using environment variables:

```bash
# .env
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_auth_domain
FIREBASE_PROJECT_ID=your_project_id
GOOGLE_DRIVE_FOLDER_ID=your_folder_id
```

## Troubleshooting 🔧

### Common Issues:

1. **Authentication not working**: Check Firebase configuration and Google OAuth setup
2. **Drive API errors**: Verify API is enabled and credentials are correct
3. **File preview issues**: Ensure files are publicly accessible or properly shared
4. **Build errors**: Check all dependencies are installed and compatible

### Debug Mode:

Enable debug logging by setting `__DEV__` flag in your code.

## Contributing 🤝

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License 📝

This project is licensed under the MIT License.

## Support 💬

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review Expo and Firebase documentation

---

**Happy Learning! 🎓**
