/**
 * Copyright (c) 650 Industries.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
import type { CodeFrame } from '../Data/parseLogBoxLog';
export declare function LogBoxInspectorCodeFrame({ codeFrame }: {
    codeFrame?: CodeFrame;
}): React.JSX.Element | null;
//# sourceMappingURL=LogBoxInspectorCodeFrame.d.ts.map