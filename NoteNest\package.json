{"name": "notenest", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clear": "expo start --clear", "tunnel": "expo start --tunnel", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "update": "eas update", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^1.24.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "expo": "~53.0.10", "expo-auth-session": "^6.2.0", "expo-crypto": "^14.1.4", "expo-document-picker": "^13.1.5", "expo-file-system": "^18.1.10", "expo-sharing": "^13.1.5", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.1.6", "firebase": "^11.8.1", "react": "19.0.0", "react-native": "0.79.3", "react-native-document-picker": "^9.3.1", "react-native-pdf": "^6.7.7", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}