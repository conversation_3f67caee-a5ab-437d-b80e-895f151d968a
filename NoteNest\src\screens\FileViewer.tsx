import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Pdf from 'react-native-pdf';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../types';
import DriveApiService from '../services/driveApi';
import { COLORS, FILE_TYPE_COLORS } from '../config/app';

type FileViewerNavigationProp = StackNavigationProp<RootStackParamList, 'FileViewer'>;
type FileViewerRouteProp = RouteProp<RootStackParamList, 'FileViewer'>;

interface FileViewerProps {
  navigation: FileViewerNavigationProp;
  route: FileViewerRouteProp;
}

const FileViewer: React.FC<FileViewerProps> = ({ navigation, route }) => {
  const { file } = route.params;
  const [loading, setLoading] = useState(true);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [downloading, setDownloading] = useState(false);

  const driveService = DriveApiService.getInstance();

  useEffect(() => {
    loadFileUrl();
  }, [file.id]);

  const loadFileUrl = async () => {
    try {
      setLoading(true);
      setError(null);

      // For Google Drive files, we can use the webViewLink directly
      // or get a download URL for better viewing
      if (file.webViewLink) {
        setDownloadUrl(file.webViewLink);
      } else {
        const url = await driveService.getFileDownloadUrl(file.id);
        if (url) {
          setDownloadUrl(url);
        } else {
          setError('Unable to load file preview. File may not be accessible.');
        }
      }
    } catch (error) {
      console.error('Error loading file URL:', error);
      setError('Failed to load file. Please check your internet connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      setDownloading(true);

      // Get the actual download URL for the file
      const actualDownloadUrl = await driveService.getFileDownloadUrl(file.id);

      if (!actualDownloadUrl) {
        Alert.alert('Error', 'Download URL not available');
        return;
      }

      // Create a local file path
      const fileUri = FileSystem.documentDirectory + file.name;

      // Download the file
      const downloadResult = await FileSystem.downloadAsync(actualDownloadUrl, fileUri);

      if (downloadResult.status === 200) {
        // Check if sharing is available
        const isAvailable = await Sharing.isAvailableAsync();

        if (isAvailable) {
          await Sharing.shareAsync(downloadResult.uri);
        } else {
          Alert.alert('Success', 'File downloaded successfully');
        }
      } else {
        Alert.alert('Error', 'Failed to download file');
      }
    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('Error', 'Failed to download file. Please try again.');
    } finally {
      setDownloading(false);
    }
  };

  const handleOpenInBrowser = () => {
    if (file.webViewLink) {
      Linking.openURL(file.webViewLink);
    } else {
      Alert.alert('Error', 'Unable to open file in browser');
    }
  };

  const getFileIcon = () => {
    if (file.mimeType.includes('pdf')) return 'document-text';
    if (file.mimeType.includes('word')) return 'document';
    if (file.mimeType.includes('powerpoint') || file.mimeType.includes('presentation')) return 'easel';
    return 'document-outline';
  };

  const getFileColor = () => {
    return (FILE_TYPE_COLORS as Record<string, string>)[file.mimeType] || FILE_TYPE_COLORS.default;
  };

  const formatFileSize = (size?: string) => {
    if (!size) return '';
    const bytes = parseInt(size);
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity 
        style={styles.backButton} 
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color={COLORS.text.primary} />
      </TouchableOpacity>
      
      <View style={styles.headerInfo}>
        <Text style={styles.fileName} numberOfLines={1}>
          {file.name}
        </Text>
        <Text style={styles.fileDetails}>
          {formatFileSize(file.size)} • {file.semester}
        </Text>
      </View>

      <View style={styles.headerActions}>
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={handleOpenInBrowser}
        >
          <Ionicons name="open-outline" size={20} color={COLORS.text.secondary} />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={handleDownload}
          disabled={downloading}
        >
          {downloading ? (
            <ActivityIndicator size="small" color={COLORS.text.secondary} />
          ) : (
            <Ionicons name="download-outline" size={20} color={COLORS.text.secondary} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading file...</Text>
        </View>
      );
    }

    if (error || !downloadUrl) {
      return (
        <View style={styles.errorContainer}>
          <View style={[styles.fileIconLarge, { backgroundColor: getFileColor() + '20' }]}>
            <Ionicons name={getFileIcon()} size={48} color={getFileColor()} />
          </View>
          <Text style={styles.errorTitle}>
            {error || 'Unable to preview this file'}
          </Text>
          <Text style={styles.errorDescription}>
            You can try downloading the file or opening it in your browser.
          </Text>
          <View style={styles.errorActions}>
            <TouchableOpacity style={styles.primaryButton} onPress={handleDownload}>
              <Ionicons name="download" size={16} color={COLORS.background} />
              <Text style={styles.primaryButtonText}>Download</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.secondaryButton} onPress={handleOpenInBrowser}>
              <Ionicons name="open" size={16} color={COLORS.primary} />
              <Text style={styles.secondaryButtonText}>Open in Browser</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // For PDF files, use react-native-pdf
    if (file.mimeType.includes('pdf')) {
      return (
        <Pdf
          source={{ uri: downloadUrl, cache: true }}
          style={styles.pdf}
          onLoadComplete={(numberOfPages) => {
            console.log(`PDF loaded with ${numberOfPages} pages`);
            setLoading(false);
          }}
          onPageChanged={(page, numberOfPages) => {
            console.log(`Current page: ${page}/${numberOfPages}`);
          }}
          onError={(error) => {
            console.error('PDF Error:', error);
            setLoading(false);
            setError('Failed to load PDF. Please try downloading or opening in browser.');
          }}
          onPressLink={(uri) => {
            console.log(`Link pressed: ${uri}`);
          }}
        />
      );
    }

    // For other file types, show a preview with options to download or open in browser
    return (
      <View style={styles.previewContainer}>
        <View style={[styles.fileIconLarge, { backgroundColor: getFileColor() + '20' }]}>
          <Ionicons name={getFileIcon()} size={64} color={getFileColor()} />
        </View>
        <Text style={styles.previewTitle}>{file.name}</Text>
        <Text style={styles.previewDescription}>
          This file type cannot be previewed directly. You can download it or open it in your browser.
        </Text>
        <View style={styles.previewActions}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleDownload}>
            <Ionicons name="download" size={16} color={COLORS.background} />
            <Text style={styles.primaryButtonText}>Download</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.secondaryButton} onPress={handleOpenInBrowser}>
            <Ionicons name="open" size={16} color={COLORS.primary} />
            <Text style={styles.secondaryButtonText}>Open in Browser</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} />
      {renderHeader()}
      <View style={styles.content}>
        {renderContent()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 2,
  },
  fileDetails: {
    fontSize: 12,
    color: COLORS.text.secondary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  pdf: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  previewContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 8,
  },
  previewDescription: {
    fontSize: 14,
    color: COLORS.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32,
  },
  previewActions: {
    flexDirection: 'row',
    gap: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  fileIconLarge: {
    width: 80,
    height: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorDescription: {
    fontSize: 14,
    color: COLORS.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32,
  },
  errorActions: {
    width: '100%',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  primaryButtonText: {
    color: COLORS.background,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  secondaryButtonText: {
    color: COLORS.primary,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default FileViewer;
