import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Linking,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { DriveFile } from '../types';
import DriveApiService from '../services/driveApi';
import { COLORS, FILE_TYPE_COLORS } from '../config/app';

interface FileViewerProps {
  navigation: any;
  route: {
    params: {
      file: DriveFile;
    };
  };
}

const { width, height } = Dimensions.get('window');

const FileViewer: React.FC<FileViewerProps> = ({ navigation, route }) => {
  const { file } = route.params;
  const [loading, setLoading] = useState(true);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [downloading, setDownloading] = useState(false);

  const driveService = DriveApiService.getInstance();

  useEffect(() => {
    loadFileUrl();
  }, [file.id]);

  const loadFileUrl = async () => {
    try {
      setLoading(true);
      setError(null);

      // For Google Drive files, we can use the webViewLink directly
      // or get a download URL for better viewing
      if (file.webViewLink) {
        setDownloadUrl(file.webViewLink);
      } else {
        const url = await driveService.getFileDownloadUrl(file.id);
        setDownloadUrl(url);
      }
    } catch (error) {
      console.error('Error loading file URL:', error);
      setError('Failed to load file. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      setDownloading(true);
      
      if (!downloadUrl) {
        Alert.alert('Error', 'Download URL not available');
        return;
      }

      // Create a local file path
      const fileUri = FileSystem.documentDirectory + file.name;
      
      // Download the file
      const downloadResult = await FileSystem.downloadAsync(downloadUrl, fileUri);
      
      if (downloadResult.status === 200) {
        // Check if sharing is available
        const isAvailable = await Sharing.isAvailableAsync();
        
        if (isAvailable) {
          await Sharing.shareAsync(downloadResult.uri);
        } else {
          Alert.alert('Success', 'File downloaded successfully');
        }
      } else {
        Alert.alert('Error', 'Failed to download file');
      }
    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('Error', 'Failed to download file. Please try again.');
    } finally {
      setDownloading(false);
    }
  };

  const handleOpenInBrowser = () => {
    if (file.webViewLink) {
      Linking.openURL(file.webViewLink);
    } else {
      Alert.alert('Error', 'Unable to open file in browser');
    }
  };

  const getFileIcon = () => {
    if (file.mimeType.includes('pdf')) return 'document-text';
    if (file.mimeType.includes('word')) return 'document';
    if (file.mimeType.includes('powerpoint') || file.mimeType.includes('presentation')) return 'easel';
    return 'document-outline';
  };

  const getFileColor = () => {
    return (FILE_TYPE_COLORS as Record<string, string>)[file.mimeType] || FILE_TYPE_COLORS.default;
  };

  const formatFileSize = (size?: string) => {
    if (!size) return '';
    const bytes = parseInt(size);
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity 
        style={styles.backButton} 
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color={COLORS.text.primary} />
      </TouchableOpacity>
      
      <View style={styles.headerInfo}>
        <Text style={styles.fileName} numberOfLines={1}>
          {file.name}
        </Text>
        <Text style={styles.fileDetails}>
          {formatFileSize(file.size)} • {file.semester}
        </Text>
      </View>

      <View style={styles.headerActions}>
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={handleOpenInBrowser}
        >
          <Ionicons name="open-outline" size={20} color={COLORS.text.secondary} />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={handleDownload}
          disabled={downloading}
        >
          {downloading ? (
            <ActivityIndicator size="small" color={COLORS.text.secondary} />
          ) : (
            <Ionicons name="download-outline" size={20} color={COLORS.text.secondary} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading file...</Text>
        </View>
      );
    }

    if (error || !downloadUrl) {
      return (
        <View style={styles.errorContainer}>
          <View style={[styles.fileIconLarge, { backgroundColor: getFileColor() + '20' }]}>
            <Ionicons name={getFileIcon()} size={48} color={getFileColor()} />
          </View>
          <Text style={styles.errorTitle}>
            {error || 'Unable to preview this file'}
          </Text>
          <Text style={styles.errorDescription}>
            You can try downloading the file or opening it in your browser.
          </Text>
          <View style={styles.errorActions}>
            <TouchableOpacity style={styles.primaryButton} onPress={handleDownload}>
              <Ionicons name="download" size={16} color={COLORS.background} />
              <Text style={styles.primaryButtonText}>Download</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.secondaryButton} onPress={handleOpenInBrowser}>
              <Ionicons name="open" size={16} color={COLORS.primary} />
              <Text style={styles.secondaryButtonText}>Open in Browser</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // For PDF files and other supported formats, use WebView
    const viewerUrl = file.mimeType.includes('pdf') 
      ? `https://docs.google.com/viewer?url=${encodeURIComponent(downloadUrl)}&embedded=true`
      : downloadUrl;

    return (
      <WebView
        source={{ uri: viewerUrl }}
        style={styles.webView}
        onLoadStart={() => setLoading(true)}
        onLoadEnd={() => setLoading(false)}
        onError={() => {
          setLoading(false);
          setError('Failed to load file preview');
        }}
        startInLoadingState={true}
        renderLoading={() => (
          <View style={styles.webViewLoading}>
            <ActivityIndicator size="large" color={COLORS.primary} />
          </View>
        )}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} />
      {renderHeader()}
      <View style={styles.content}>
        {renderContent()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 2,
  },
  fileDetails: {
    fontSize: 12,
    color: COLORS.text.secondary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  webView: {
    flex: 1,
  },
  webViewLoading: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  fileIconLarge: {
    width: 80,
    height: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorDescription: {
    fontSize: 14,
    color: COLORS.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32,
  },
  errorActions: {
    width: '100%',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  primaryButtonText: {
    color: COLORS.background,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  secondaryButtonText: {
    color: COLORS.primary,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default FileViewer;
