import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { DriveFile, Semester } from '../types';
import DriveApiService from '../services/driveApi';
import { COLORS, FILE_TYPE_ICONS, FILE_TYPE_COLORS } from '../config/app';

interface SemesterScreenProps {
  navigation: any;
  route: {
    params: {
      semester: Semester;
    };
  };
}

const SemesterScreen: React.FC<SemesterScreenProps> = ({ navigation, route }) => {
  const { semester } = route.params;
  const [files, setFiles] = useState<DriveFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const driveService = DriveApiService.getInstance();

  const loadFiles = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const fileData = await driveService.getFilesInSemester(semester.id);
      setFiles(fileData);
    } catch (error) {
      console.error('Error loading files:', error);
      setError('Failed to load files. Please check your internet connection.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadFiles();
  }, [semester.id]);

  const handleRefresh = () => {
    loadFiles(true);
  };

  const handleFilePress = (file: DriveFile) => {
    navigation.navigate('FileViewer', { file });
  };

  const getFileIcon = (mimeType: string) => {
    return FILE_TYPE_ICONS[mimeType as keyof typeof FILE_TYPE_ICONS] ?? FILE_TYPE_ICONS.default;
  };

  const getFileColor = (mimeType: string) => {
    return FILE_TYPE_COLORS[mimeType as keyof typeof FILE_TYPE_COLORS] ?? FILE_TYPE_COLORS.default;
  };

  const formatFileSize = (size?: string) => {
    if (!size) return '';
    const bytes = parseInt(size);
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const renderFileCard = ({ item }: { item: DriveFile }) => (
    <TouchableOpacity
      style={styles.fileCard}
      onPress={() => handleFilePress(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.fileIcon, { backgroundColor: getFileColor(item.mimeType) + '20' }]}>
        <Ionicons 
          name="document" 
          size={24} 
          color={getFileColor(item.mimeType)} 
        />
      </View>
      <View style={styles.fileInfo}>
        <Text style={styles.fileName} numberOfLines={2}>
          {item.name}
        </Text>
        <View style={styles.fileDetails}>
          <Text style={styles.fileSize}>{formatFileSize(item.size)}</Text>
          <Text style={styles.fileDot}>•</Text>
          <Text style={styles.fileDate}>{formatDate(item.modifiedTime)}</Text>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color={COLORS.text.light} />
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-outline" size={64} color={COLORS.text.light} />
      <Text style={styles.emptyTitle}>No Files Found</Text>
      <Text style={styles.emptyDescription}>
        This semester folder doesn't contain any supported files yet.
      </Text>
      <TouchableOpacity style={styles.retryButton} onPress={() => loadFiles()}>
        <Text style={styles.retryButtonText}>Refresh</Text>
      </TouchableOpacity>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="alert-circle-outline" size={64} color={COLORS.error} />
      <Text style={styles.emptyTitle}>Error Loading Files</Text>
      <Text style={styles.emptyDescription}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={() => loadFiles()}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>{semester.name}</Text>
          <Text style={styles.headerSubtitle}>
            {files.length} {files.length === 1 ? 'file' : 'files'}
          </Text>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading files...</Text>
          </View>
        ) : error ? (
          renderErrorState()
        ) : files.length === 0 ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={files}
            renderItem={renderFileCard}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[COLORS.primary]}
                tintColor={COLORS.primary}
              />
            }
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.text.secondary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  listContainer: {
    paddingBottom: 20,
  },
  fileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  fileIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text.primary,
    marginBottom: 6,
    lineHeight: 22,
  },
  fileDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileSize: {
    fontSize: 12,
    color: COLORS.text.secondary,
  },
  fileDot: {
    fontSize: 12,
    color: COLORS.text.light,
    marginHorizontal: 6,
  },
  fileDate: {
    fontSize: 12,
    color: COLORS.text.secondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.text.secondary,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: COLORS.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: COLORS.background,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SemesterScreen;
