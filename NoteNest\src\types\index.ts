// Type definitions for NoteNest app

export interface User {
  id: string;
  email: string;
  name: string;
  photoURL?: string;
  accessToken: string;
}

export interface DriveFile {
  id: string;
  name: string;
  mimeType: string;
  size?: string;
  modifiedTime: string;
  webViewLink?: string;
  downloadUrl?: string;
  thumbnailLink?: string;
  semester: string;
}

export interface Semester {
  id: string;
  name: string;
  fileCount: number;
  files: DriveFile[];
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface DriveState {
  semesters: Semester[];
  loading: boolean;
  error: string | null;
  searchResults: DriveFile[];
  isSearching: boolean;
}

export interface AppConfig {
  googleDriveFolderId: string; // The main shared folder ID
  firebaseConfig: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
  };
}

export type RootStackParamList = {
  Login: undefined;
  Main: undefined;
  Home: undefined;
  Semester: { semester: Semester };
  FileViewer: { file: DriveFile };
  Search: undefined;
};

export type FileType = 'pdf' | 'doc' | 'docx' | 'ppt' | 'pptx' | 'txt' | 'unknown';

export interface SearchFilters {
  semester?: string;
  fileType?: FileType;
  dateRange?: {
    start: Date;
    end: Date;
  };
}
